#!/bin/bash

# Integration test script for servicegraph-builder with distributed tracing
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Default values
NAMESPACE="default"
RELEASE_NAME="test-servicegraph"
CLEANUP=false
SKIP_BUILD=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --namespace|-n)
            NAMESPACE="$2"
            shift 2
            ;;
        --release|-r)
            RELEASE_NAME="$2"
            shift 2
            ;;
        --cleanup|-c)
            CLEANUP=true
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --namespace, -n    Kubernetes namespace (default: default)"
            echo "  --release, -r      Helm release name (default: test-servicegraph)"
            echo "  --cleanup, -c      Clean up resources after test"
            echo "  --skip-build       Skip building Docker images"
            echo "  --help, -h         Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_status "Starting integration test for servicegraph-builder"
print_status "Namespace: $NAMESPACE"
print_status "Release: $RELEASE_NAME"

# Check prerequisites
print_step "Checking prerequisites..."
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

if ! command -v helm &> /dev/null; then
    print_error "helm is not installed or not in PATH"
    exit 1
fi

if ! command -v docker &> /dev/null; then
    print_error "docker is not installed or not in PATH"
    exit 1
fi

# Build Docker images if not skipped
if [ "$SKIP_BUILD" = false ]; then
    print_step "Building Docker images..."
    
    # Build servicegraph-builder
    print_status "Building servicegraph-builder Docker image..."
    cd servicegraph-builder
    docker build -t servicegraph-builder:latest -f docker/Dockerfile.servicegraph .
    cd ..
    
    # Build microservices
    print_status "Building microservices Docker images..."
    cd simple-microservices
    make docker-build
    cd ..
else
    print_warning "Skipping Docker image builds"
fi

# Deploy servicegraph-helm
print_step "Deploying servicegraph-helm chart..."
helm upgrade --install "$RELEASE_NAME" ./servicegraph-helm \
    --namespace "$NAMESPACE" \
    --create-namespace \
    --wait \
    --timeout=300s

# Deploy simple-microservices
print_step "Deploying simple-microservices..."
cd simple-microservices
kubectl apply -f k8s/ --namespace="$NAMESPACE"
kubectl wait --for=condition=ready pod -l app=gateway --namespace="$NAMESPACE" --timeout=120s
kubectl wait --for=condition=ready pod -l app=user-service --namespace="$NAMESPACE" --timeout=120s
kubectl wait --for=condition=ready pod -l app=data-service --namespace="$NAMESPACE" --timeout=120s
cd ..

# Wait for servicegraph-builder to be ready
print_step "Waiting for servicegraph-builder to be ready..."
kubectl wait --for=condition=ready pod -l app.kubernetes.io/component=servicegraph-builder --namespace="$NAMESPACE" --timeout=120s

print_step "Testing trace flow..."

# Port forward to access services
print_status "Setting up port forwards..."
kubectl port-forward service/gateway 8080:8080 --namespace="$NAMESPACE" &
GATEWAY_PF_PID=$!

kubectl port-forward service/"$RELEASE_NAME"-servicegraph-builder 8083:8083 --namespace="$NAMESPACE" &
SERVICEGRAPH_PF_PID=$!

# Wait for port forwards to be ready
sleep 5

# Function to cleanup port forwards
cleanup_port_forwards() {
    if [ ! -z "$GATEWAY_PF_PID" ]; then
        kill $GATEWAY_PF_PID 2>/dev/null || true
    fi
    if [ ! -z "$SERVICEGRAPH_PF_PID" ]; then
        kill $SERVICEGRAPH_PF_PID 2>/dev/null || true
    fi
}

# Trap to cleanup on exit
trap cleanup_port_forwards EXIT

# Generate some test traffic
print_status "Generating test traffic..."
for i in {1..5}; do
    print_status "Creating test user $i..."
    curl -X POST http://localhost:8080/users \
        -H "Content-Type: application/json" \
        -d "{\"name\":\"Test User $i\",\"email\":\"test$<EMAIL>\"}" \
        -w "\nStatus: %{http_code}\n" \
        --max-time 10 || print_warning "Request $i failed"
    
    sleep 2
    
    print_status "Getting user $i..."
    curl -X GET "http://localhost:8080/users/$i" \
        -w "\nStatus: %{http_code}\n" \
        --max-time 10 || print_warning "Get request $i failed"
    
    sleep 1
done

# Wait for traces to propagate
print_status "Waiting for traces to propagate..."
sleep 10

# Check servicegraph-builder logs for "Converted SimpleSpan" messages
print_step "Checking servicegraph-builder logs for trace processing..."
SERVICEGRAPH_POD=$(kubectl get pods -l app.kubernetes.io/component=servicegraph-builder --namespace="$NAMESPACE" -o jsonpath='{.items[0].metadata.name}')

if [ -z "$SERVICEGRAPH_POD" ]; then
    print_error "Could not find servicegraph-builder pod"
    exit 1
fi

print_status "ServiceGraph Builder pod: $SERVICEGRAPH_POD"
print_status "Checking logs for 'Converted SimpleSpan' messages..."

CONVERTED_SPANS=$(kubectl logs "$SERVICEGRAPH_POD" --namespace="$NAMESPACE" | grep "Converted SimpleSpan" | wc -l)

if [ "$CONVERTED_SPANS" -gt 0 ]; then
    print_status "✅ Found $CONVERTED_SPANS 'Converted SimpleSpan' log entries!"
    print_status "Sample log entries:"
    kubectl logs "$SERVICEGRAPH_POD" --namespace="$NAMESPACE" | grep "Converted SimpleSpan" | head -3
else
    print_error "❌ No 'Converted SimpleSpan' log entries found"
    print_status "ServiceGraph Builder logs:"
    kubectl logs "$SERVICEGRAPH_POD" --namespace="$NAMESPACE" --tail=20
fi

# Show component status
print_step "Component status summary:"
kubectl get pods -l app.kubernetes.io/instance="$RELEASE_NAME" --namespace="$NAMESPACE"

# Cleanup if requested
if [ "$CLEANUP" = true ]; then
    print_step "Cleaning up resources..."
    cd simple-microservices
    kubectl delete -f k8s/ --namespace="$NAMESPACE" || true
    cd ..
    helm uninstall "$RELEASE_NAME" --namespace="$NAMESPACE" || true
    print_status "Cleanup complete"
fi

print_status "Integration test completed!"
if [ "$CONVERTED_SPANS" -gt 0 ]; then
    print_status "✅ SUCCESS: Traces are flowing through servicegraph-builder"
else
    print_error "❌ FAILURE: Traces are not reaching servicegraph-builder"
    exit 1
fi
