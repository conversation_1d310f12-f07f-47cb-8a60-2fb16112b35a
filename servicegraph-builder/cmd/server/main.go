package main

import (
	"os"
	"context"
    colmetricpb "go.opentelemetry.io/proto/otlp/collector/metrics/v1"

	"fmt"
	"net"	


	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc"
)

type MetricsServiceServer struct {

    colmetricpb.UnimplementedMetricsServiceServer
}

// type Span struct {

// }

func (s *MetricsServiceServer) Export(ctx context.Context, req *colmetricpb.ExportMetricsServiceRequest) (*colmetricpb.ExportMetricsServiceResponse, error) {
    // Handle metrics here
    return &colmetricpb.ExportMetricsServiceResponse{}, nil
}

func main() {
	// Setup logging
	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})

	lis, err := net.Listen("tcp", fmt.Sprintf("localhost:%d", ))


	
}
