{{- if .Values.beyla.enabled }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "servicegraph.beyla.serviceAccountName" . }}
  namespace: {{ include "servicegraph.namespace" . }}
  labels:
    {{- include "servicegraph.beyla.labels" . | nindent 4 }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "servicegraph.beyla.serviceAccountName" . }}
  labels:
    {{- include "servicegraph.beyla.labels" . | nindent 4 }}
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "nodes"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets", "daemonsets", "statefulsets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["extensions"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "servicegraph.beyla.serviceAccountName" . }}
  labels:
    {{- include "servicegraph.beyla.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "servicegraph.beyla.serviceAccountName" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "servicegraph.beyla.serviceAccountName" . }}
  namespace: {{ include "servicegraph.namespace" . }}
{{- end }}
