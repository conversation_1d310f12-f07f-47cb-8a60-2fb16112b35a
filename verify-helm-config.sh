#!/bin/bash

# Script to verify Helm chart configuration
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Verifying Helm chart configuration..."

# Check if helm is available
if ! command -v helm &> /dev/null; then
    print_error "helm is not installed or not in PATH"
    exit 1
fi

# Dry run to validate templates
print_status "Running Helm template dry-run..."
if helm template test-servicegraph ./servicegraph-helm > /tmp/helm-output.yaml; then
    print_status "✅ Helm templates are valid"
else
    print_error "❌ Helm template validation failed"
    exit 1
fi

# Check for servicegraph-builder components
print_status "Checking for servicegraph-builder components in rendered templates..."

if grep -q "servicegraph-builder" /tmp/helm-output.yaml; then
    print_status "✅ ServiceGraph Builder components found"
else
    print_error "❌ ServiceGraph Builder components not found"
    exit 1
fi

# Check OTel Collector configuration
print_status "Checking OTel Collector configuration..."
if grep -q "servicegraph-builder:8083" /tmp/helm-output.yaml; then
    print_status "✅ OTel Collector configured to export to ServiceGraph Builder"
else
    print_warning "⚠️  OTel Collector may not be configured correctly"
fi

# Check service definitions
print_status "Checking service definitions..."
SERVICES=$(grep -c "kind: Service" /tmp/helm-output.yaml)
print_status "Found $SERVICES services in the chart"

# Check deployment definitions
DEPLOYMENTS=$(grep -c "kind: Deployment" /tmp/helm-output.yaml)
print_status "Found $DEPLOYMENTS deployments in the chart"

# Check DaemonSet (Beyla)
DAEMONSETS=$(grep -c "kind: DaemonSet" /tmp/helm-output.yaml)
print_status "Found $DAEMONSETS daemonsets in the chart"

# Show key configuration
print_status "Key configuration summary:"
echo "Services: $SERVICES (expected: 2 - OTel Collector + ServiceGraph Builder)"
echo "Deployments: $DEPLOYMENTS (expected: 2 - OTel Collector + ServiceGraph Builder)"  
echo "DaemonSets: $DAEMONSETS (expected: 1 - Beyla)"

# Clean up
rm -f /tmp/helm-output.yaml

print_status "✅ Helm chart configuration verification complete!"
